<template>
  <div class="_fw">
    <email-field
        @focus="setFocus"
        @blur="setBlur"
        v-model="form.email"
        @input="emit('update:email', $event)"
        input-class="font-3-4r"
        placeholder="Email"
        dense
        hide-bottom-space
        @update:valid="setValid"
    ></email-field>

    <q-slide-transition>
      <div v-if="valid" class="_fw q-py-lg">
        <template v-if="retry">
          <div class="text-ir-text tw-five font-1r">This device doesn't have a passkey yet</div>
          <div class="_fw">
            <q-chip outline color="primary" clickable @click="tryAddingDevice">
              <q-icon color="primary" name="mdi-key-arrow-right"></q-icon>
              <span class="q-ml-sm text-ir-text">Add Device Passkey</span>
            </q-chip>
          </div>
        </template>
        <template v-else-if="loggingIn">
          <q-chip color="transparent">
            <q-spinner color="primary"></q-spinner>
            <span class="q-ml-sm">{{log}}</span>
          </q-chip>
        </template>
        <div class="q-pa-sm font-1r tw-five text-red" v-else-if="error">{{ error }}</div>
        <template v-else>
          <q-chip color="transparent" v-if="loginLoading">
            <q-spinner color="primary"></q-spinner>
            <span class="q-ml-sm">Searching for existing account</span>
          </q-chip>
          <template v-if="existing">
            <q-chip v-if="loadingKeys" color="transparent">
              <q-spinner color="accent"></q-spinner>
              <span class="q-ml-sm">Checking device keychain</span>
            </q-chip>
            <q-chip clickable outline color="accent" v-else-if="!keys.length" @click="runLogin">
              <q-icon color="accent" name="mdi-key"></q-icon>
              <span class="q-ml-sm text-ir-text">Login using device security</span>
            </q-chip>
            <q-chip v-else outline color="primary" clickable @click="runLogin">
              <q-icon color="primary" name="mdi-key-arrow-right"></q-icon>
              <span class="q-ml-sm text-ir-text">Login using device security</span>
            </q-chip>
            <q-chip color="transparent">
              <q-icon color="green" name="mdi-check-circle"></q-icon>
              <span class="q-ml-sm">Account Found</span>
            </q-chip>
          </template>
          <q-chip clickable outline color="accent" v-else @click="runLogin">
            <q-icon color="accent" name="mdi-key"></q-icon>
            <span class="q-ml-sm text-ir-text">Login using device security</span>
          </q-chip>
        </template>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>
  import EmailField from 'components/common/input/EmailField.vue';

  import {computed, ref, watch} from 'vue';
  import {getRpID, isWebAuthnSupported} from 'components/auth/webauthn/webauthn';
  import {usePasskeys} from 'stores/passkeys';
  import {useLogins} from 'stores/logins';
  import { registerPasskey, loginWithPasskey } from 'components/auth/webauthn/webauthn';

  const keyStore = usePasskeys();
  const loginStore = useLogins();

  const emit = defineEmits(['update:email','update:on', 'focus', 'blur', 'update:tab']);
  const props = defineProps({
    on: Boolean,
    type: { type: String, default: 'email' },
    email: String
  })

  const focusOn = ref(false);
  const matchTotal = ref(0);
  const existing = ref(undefined);
  const loadingKeys = ref(false);
  const loginLoading = ref(false);
  const loggingIn = ref(false);
  const log = ref('')
  const error = ref('');
  const keys = ref([])
  const valid = ref(false);
  const retry = ref(false);

  let settingFocus = ref(false);
  const setFocus = () => {
    settingFocus.value = true;
    focusOn.value = true;
    emit('focus');
    setTimeout(() => {
      settingFocus.value = false;
    }, 500);
  };
  const setBlur = () => {
    setTimeout(() => {
      if (!settingFocus.value) {
        focusOn.value = false;
        emit('blur');
      }
    }, 250);
  };


  const formDef = (defs) => {
    return {
      email: '',
      ...defs
    };
  };
  const form = ref(formDef());

  watch(() => props.email, (nv) => {
    if (nv) form.value.email = nv;
  }, { immediate: true })


  const tryAddingDevice = async () => {
    try {
      const res = await registerPasskey({ login: existing.value })
          .catch(err => {
            console.error(`Error retrying passkey: ${err.message}`)
            error.value = `Failed to register passkey: ${err.message}`;
            return {}
          })
      if (res.success) existing.value = res.login;
      else {
        if (!error.value) error.value = res.message;
        return;
      }
    } catch (e){
      console.error(`Error registering passkey: ${e.message}`)
      error.value = 'Failed to register passkey';
    }
  }
  const runLogin = async () => {
    try {
      loggingIn.value = true;
      if (!existing.value || !keys.value.length) {
        const res = await registerPasskey({ login: { ...existing.value, ...form.value } })
            .catch(err => {
              console.error(`Error logging in with passkey: ${err.message}`)
              if (retry.value) error.value = `Failed to register passkey: ${err.message}`;
              else retry.value = true;
              return {}
            })
        loggingIn.value = false;
        if (res.login) return existing.value = res.login;
        else {
          if (!error.value) error.value = res.message;
          return;
        }
      }
      const res = await loginWithPasskey({ login: existing.value })
          .catch(err => {
            console.error(`Error verifying passkey: ${err.message}`)
            error.value = `Failed to verify passkey: ${err.message}`;
            return {}
          })
      loggingIn.value = false;
      // console.log('login res', res)
      if (res.success) {
        return existing.value = res.login;
      } else {
        if (!error.value) error.value = res.message;
        return;
      }
    } catch (e){
      console.error(`Error verifying passkey: ${e.message}`)
      error.value = 'Failed to verify passkey';
      loggingIn.value = false;
    }
  }

  let lastValid = ref('');
  const setValid = async (val) => {
    valid.value = val;
    if (val) {
      setTimeout(async () => {
        const check = form.value[props.type];
        if (check !== lastValid.value) {
          lastValid.value = check;
          const query = { $limit: 1, [props.type]: check };
          loginLoading.value = true;
          const { total, data } = await loginStore.find({ query, loginOptions: { existCheck: true } })
              .catch(err => {
                console.error(`Error checking login: ${err.message}`)
                error.value = 'Failed to check for an existing login';
              });
          loginLoading.value = false;
          matchTotal.value = total;
          existing.value = data[0];
          if (total) {
            loadingKeys.value = true;
            const rpID = getRpID();
            const keyFetch = await keyStore.find({ query: { $limit: 1, login: existing.value._id, rpID } })
                .catch(err => {
                  console.error(`Error fetching keys: ${err.message}`)
                  error.value = 'Failed to search for validation keys';
                });
            loadingKeys.value = false;
            if (keyFetch.data) keys.value = keyFetch.data;
            if(!keyFetch.total && existing.value.isVerified) emit('update:tab', 'email')
          }
        }
      }, 20);
    }
  };

  const isWebAuthn = computed(() => isWebAuthnSupported());
  watch(isWebAuthn, (nv) => {
    emit('update:on', !!nv);
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
