// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    pingsDataValidator,
    pingsPatchValidator,
    pingsQueryValidator,
    pingsResolver,
    pingsExternalResolver,
    pingsDataResolver,
    pingsPatchResolver,
    pingsQueryResolver
} from './pings.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {PingsService, getOptions} from './pings.class.js'
import {pingsPath, pingsMethods} from './pings.shared.js'

export * from './pings.class.js'
export * from './pings.schema.js'

import {sendPings, resendPings} from './utils/index.js';
import {_get, getJoin, logChange} from '../../utils/index.js';
import {allUcanAuth, anyAuth, CapabilityParts} from 'feathers-ucan';

const joinSubject = async (context: HookContext): Promise<HookContext> => {
    return await getJoin({
        servicePath: 'subjectService',
        herePath: 'subject'
    })(context)
};

const updater = [['pings', 'WRITE']] as Array<CapabilityParts>;
const deleter = [['pings', '*']] as Array<CapabilityParts>;
const ucanArgs = {
    create: anyAuth,
    patch: [...updater],
    update: [...updater],
    remove: [...deleter]
}
const authenticate = async (context: HookContext) => {
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['recipient'], '*']],
        or: '*'
    })(context) as any;
}

/** log properties
 * @param{object} data: the patch data for the update - must be a top level mongo $ operator and these operators must be a flat object. Will be scrubbed to only include allowed paths
 * @param{string} service: the service to patch
 * @param{string} subject: the id of the record to patch
 */
const logPings = async (context: HookContext) => {
    const {log} = context.params.runJoin || {}
    if (log) {
        const allowedPaths = {
            'ppls': [/^invites\.[^.]+\.[Rr]eminded$/]
        }
        const scrubbedData: any = {}

        const checkKey = (k, subk) => {
            if ((allowedPaths[log.service] || []).some(a => a.test(k))) {
                if(!scrubbedData[subk]) scrubbedData[subk] = {}
                scrubbedData[subk][k] = data[subk][k]
            }
        }
        for (const k in data) {
            if (!['$addToSet', '$set', '$unset', '$push', '$pull'].includes(k)) continue
            for (const subk in data[k]) {
                checkKey(k, subk)
            }
        }
        await new CoreCall(log.service, context).patch(log.subject, scrubbedData, {admin_pass:true})
            .catch(err => console.error(`Error logging ping data: ${err.message}`))
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const pings = (app: Application) => {
    // Register our service on the Feathers application
    app.use(pingsPath, new PingsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: pingsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(pingsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(pingsExternalResolver),
                schemaHooks.resolveResult(pingsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(pingsQueryValidator),
                schemaHooks.resolveQuery(pingsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(pingsDataValidator),
                schemaHooks.resolveData(pingsDataResolver),
                sendPings
            ],
            patch: [
                schemaHooks.validateData(pingsPatchValidator),
                schemaHooks.resolveData(pingsPatchResolver),
                resendPings
            ],
            remove: []
        },
        after: {
            all: [
                joinSubject
            ],
            find: [],
            create: [logPings],
            patch: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [pingsPath]: PingsService
    }
}
