// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    commonFields,
    imageSchema,
    phoneSchema,
    addressSchema,
    commonQueries,
    addToSet,
    trimHandler, pull, commonPatch
} from '../../utils/common/schemas.js';


// Main data model schema
export const pplsSchema = {
    $id: 'Ppls',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        _id: ObjectIdSchema(),
        name: {type: 'string'},
        lastName: {type: 'string'},
        firstName: {type: 'string'},
        did: {type: 'string'},
        fid: {type: 'string'},
        wallet: ObjectIdSchema(),
        address: addressSchema,
        addresses: {type: 'array', items: addressSchema},
        email: {anyOf: [{type: 'string'}, {type: 'null'}]},
        emails: {type: 'array', items: {type: 'string'}},
        phone: {anyOf: [{type: 'null'}, phoneSchema]},
        phones: {type: 'array', items: phoneSchema},
        household: ObjectIdSchema(),
        login: ObjectIdSchema(),
        avatar: imageSchema,
        online: {type: 'boolean'},
        onlineAt: {},
        ramp_user_id: { type: 'string' },
        moovCardholderId: {type: 'string'},
        ims: {type: 'array', items: ObjectIdSchema()},
        moovAccounts: {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    type: 'object', properties: {
                        id: {type: 'string'},
                        isController: {type: 'boolean'}
                    }
                }
            }
        },
        cams: {type: 'array', items: ObjectIdSchema()},
        inGroups: {type: 'array', items: ObjectIdSchema()},
        inOrgs: {type: 'array', items: ObjectIdSchema()},
        lastGroupSync: {type: 'string'},
        refs: {type: 'array', items: ObjectIdSchema()},
        badges: {type: 'array', items: {type: 'string'}},
        dob: {type: 'string'},
        ssn: {type: 'string'},
        itin: {type: 'string'},
        last4Itin: {type: 'string'},
        last4Ssn: {type: 'string'},
        gender: {type: 'string'},
        cleanupFlag: {type: 'boolean'},
        enrollments: {type: 'array', items: ObjectIdSchema()},
        invites: {type: 'object', patternProperties: {
            "^.*$": { //Should be the org id you are being invited to join
                type: 'object', properties: {
                    by: ObjectIdSchema(),
                    at: {},
                    reminded: [{ type: 'array', items: {}}],
                    caps: {
                        type: 'object',
                        patternProperties: {
                            "^.*$": {
                                type: 'object', required: ['id', 'path'], properties: {
                                    id: ObjectIdSchema(),
                                    path: {type: 'string', $comment: 'This is the path of the caps.cap permission set for this person'}
                                }
                            }
                        }
                    }
                }
            }
        }},
        preferences: {
            type: 'object',
            properties: {
                notifications: {
                    type: 'object',
                    properties: {
                        unsub: {type: 'array', items: {type: 'string'}}, //categories or '*' for all
                        emailUnsub: {type: 'array', items: {type: 'string'}},
                        smsUnsub: {type: 'array', items: {type: 'string'}}
                    }
                }
            }
        },
        card_user: {type: 'array', items: ObjectIdSchema()},
        budget_user: {type: 'array', items: ObjectIdSchema()},
        ...commonFields.properties
    }
} as const

export
type Ppls = FromSchema<typeof pplsSchema>
export const pplsValidator = getValidator(pplsSchema, dataValidator)
export const pplsResolver = resolve<Ppls, HookContext>({
    firstName: trimHandler({}),
    lastName: trimHandler({}),
    name: trimHandler({})
})

export const pplsExternalResolver = resolve<Ppls, HookContext>({})

// Schema for creating new data
export const pplsDataSchema = {
    $id: 'PplsData',
    type: 'object',
    additionalProperties: true,
    required: [],
    properties: {
        ...pplsSchema.properties
    }
} as const
export type PplsData = FromSchema<typeof pplsDataSchema>
export const pplsDataValidator = getValidator(pplsDataSchema, dataValidator)
export const pplsDataResolver = resolve<PplsData, HookContext>({})

const pushPull = [
    {path: 'inGroups', type: ObjectIdSchema()},
    {path: 'card_user', type: ObjectIdSchema()},
    {path: 'budget_user', type: ObjectIdSchema()},
    {path: 'care_account_user', type: ObjectIdSchema()},
    {path: 'inOrgs', type: ObjectIdSchema()},
    {path: 'cams', type: ObjectIdSchema()},
    {path: 'phones', type: phoneSchema},
    {path: 'emails', type: {type: 'string'}},
    {path: 'addresses', type: addressSchema},
]
// Schema for updating existing data
export const pplsPatchSchema = {
    $id: 'PplsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...commonPatch(pplsSchema.properties).properties,
        ...pplsSchema.properties,
        $addToSet: addToSet(pushPull, { additionalProperties: true }),
        $pull: pull(pushPull),
        $push: addToSet(pushPull)
    }
} as const
export type PplsPatch = FromSchema<typeof pplsPatchSchema>
export const pplsPatchValidator = getValidator(pplsPatchSchema, dataValidator)
export const pplsPatchResolver = resolve<PplsPatch, HookContext>({})

// Schema for allowed query properties
export const pplsQuerySchema = {
    $id: 'PplsQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax({
            ...pplsSchema.properties,
            ...commonQueries.properties,
            cams: {
                anyOf: [
                    {type: 'array', items: ObjectIdSchema()},
                    {
                        type: 'object',
                        properties: {$exists: {type: 'boolean'}}
                    }
                ]
            },
            name: {},
            email: {},
            phone: {},
            firstName: {},
            lastName: {}
        }),
        login: {
            anyOf: [ObjectIdSchema(), {type: 'object', properties: {$exists: {type: 'boolean'}}}]
        }
    }
} as const
export type PplsQuery = FromSchema<typeof pplsQuerySchema>
export const pplsQueryValidator = getValidator(pplsQuerySchema, queryValidator)
export const pplsQueryResolver = resolve<PplsQuery, HookContext>({})
